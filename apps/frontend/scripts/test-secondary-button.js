/**
 * 普通按键功能验证脚本
 * 用于验证SecondaryButton组件的基础功能
 */

console.log('=== 普通按键功能验证开始 ===');

// 测试1: 文件结构验证
function testFileStructure() {
  console.log('\n1. 验证文件结构:');
  
  const fs = require('fs');
  const path = require('path');
  
  const requiredFiles = [
    'frontend/ButtonCodes/secondary/style/style_secondary.ts',
    'frontend/ButtonCodes/secondary/event/event_secondary.ts',
    'frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx',
    'frontend/ButtonCodes/index.ts'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    if (fs.existsSync(fullPath)) {
      console.log(`✓ ${file} 存在`);
    } else {
      console.error(`✗ ${file} 不存在`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// 测试2: 文件内容验证
function testFileContent() {
  console.log('\n2. 验证文件内容:');
  
  const fs = require('fs');
  const path = require('path');
  
  try {
    // 验证样式文件
    const styleFile = path.join(process.cwd(), 'frontend/ButtonCodes/secondary/style/style_secondary.ts');
    const styleContent = fs.readFileSync(styleFile, 'utf8');
    
    if (styleContent.includes('SecondaryButtonStyleConfig') && 
        styleContent.includes('React.CSSProperties')) {
      console.log('✓ 样式文件内容正确');
    } else {
      console.error('✗ 样式文件内容不完整');
      return false;
    }
    
    // 验证事件文件
    const eventFile = path.join(process.cwd(), 'frontend/ButtonCodes/secondary/event/event_secondary.ts');
    const eventContent = fs.readFileSync(eventFile, 'utf8');
    
    if (eventContent.includes('SecondaryButtonEventHandler') && 
        eventContent.includes('toggle') && 
        eventContent.includes('instant')) {
      console.log('✓ 事件文件内容正确');
    } else {
      console.error('✗ 事件文件内容不完整');
      return false;
    }
    
    // 验证组件文件
    const componentFile = path.join(process.cwd(), 'frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx');
    const componentContent = fs.readFileSync(componentFile, 'utf8');
    
    if (componentContent.includes('SecondaryButton') && 
        componentContent.includes('React.FC') && 
        componentContent.includes('useState')) {
      console.log('✓ 组件文件内容正确');
    } else {
      console.error('✗ 组件文件内容不完整');
      return false;
    }
    
    // 验证导出文件
    const indexFile = path.join(process.cwd(), 'frontend/ButtonCodes/index.ts');
    const indexContent = fs.readFileSync(indexFile, 'utf8');
    
    if (indexContent.includes('SecondaryButton') && 
        indexContent.includes('export')) {
      console.log('✓ 导出文件内容正确');
    } else {
      console.error('✗ 导出文件内容不完整');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('✗ 文件内容验证失败:', error.message);
    return false;
  }
}

// 测试3: TypeScript类型验证
function testTypeScriptTypes() {
  console.log('\n3. 验证TypeScript类型:');
  
  const fs = require('fs');
  const path = require('path');
  
  try {
    const styleFile = path.join(process.cwd(), 'frontend/ButtonCodes/secondary/style/style_secondary.ts');
    const styleContent = fs.readFileSync(styleFile, 'utf8');
    
    const requiredTypes = [
      'SecondaryButtonStyleConfig',
      'SecondaryButtonProps',
      'React.CSSProperties'
    ];
    
    let allTypesFound = true;
    
    requiredTypes.forEach(type => {
      if (styleContent.includes(type)) {
        console.log(`✓ 类型 ${type} 定义正确`);
      } else {
        console.error(`✗ 类型 ${type} 未找到`);
        allTypesFound = false;
      }
    });
    
    return allTypesFound;
  } catch (error) {
    console.error('✗ TypeScript类型验证失败:', error.message);
    return false;
  }
}

// 测试4: 功能特性验证
function testFeatures() {
  console.log('\n4. 验证功能特性:');
  
  const fs = require('fs');
  const path = require('path');
  
  try {
    const eventFile = path.join(process.cwd(), 'frontend/ButtonCodes/secondary/event/event_secondary.ts');
    const eventContent = fs.readFileSync(eventFile, 'utf8');
    
    const requiredFeatures = [
      'toggle', // 持续状态模式
      'instant', // 瞬时状态模式
      'handleClick',
      'handleMouseDown',
      'handleMouseUp',
      'handleMouseEnter',
      'handleMouseLeave'
    ];
    
    let allFeaturesFound = true;
    
    requiredFeatures.forEach(feature => {
      if (eventContent.includes(feature)) {
        console.log(`✓ 功能 ${feature} 实现正确`);
      } else {
        console.error(`✗ 功能 ${feature} 未实现`);
        allFeaturesFound = false;
      }
    });
    
    return allFeaturesFound;
  } catch (error) {
    console.error('✗ 功能特性验证失败:', error.message);
    return false;
  }
}

// 执行所有测试
function runAllTests() {
  const tests = [
    { name: '文件结构验证', fn: testFileStructure },
    { name: '文件内容验证', fn: testFileContent },
    { name: 'TypeScript类型验证', fn: testTypeScriptTypes },
    { name: '功能特性验证', fn: testFeatures }
  ];
  
  let passedTests = 0;
  const totalTests = tests.length;
  
  tests.forEach(test => {
    if (test.fn()) {
      passedTests++;
    }
  });
  
  console.log('\n=== 验证结果 ===');
  console.log(`通过: ${passedTests}/${totalTests}`);
  console.log(`状态: ${passedTests === totalTests ? '✓ 全部通过' : '✗ 部分失败'}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 普通按键功能实现完成！');
    console.log('✓ 所有必需的文件已创建');
    console.log('✓ TypeScript类型定义正确');
    console.log('✓ 事件处理逻辑完整');
    console.log('✓ 组件结构符合规范');
  } else {
    console.log('\n⚠️  部分功能需要完善');
  }
  
  console.log('=== 普通按键功能验证完成 ===\n');
  
  return passedTests === totalTests;
}

// 执行验证
runAllTests();
