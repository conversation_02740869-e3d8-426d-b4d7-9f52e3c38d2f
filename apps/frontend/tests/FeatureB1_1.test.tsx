import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1'

// Mock the store
const mockSetInitializeButton = jest.fn()
const mockSetCoordinateButton = jest.fn()

jest.mock('../../../frontend/features/logic/logicB1_1', () => ({
  useMatrixStore: () => ({
    coordinateButtonActive: false,
    setInitializeButton: mockSetInitializeButton,
    setCoordinateButton: mockSetCoordinateButton,
  })
}))

describe('FeatureB1_1 矩阵功能面板', () => {
  const defaultProps = {
    containerHeight: 600,
    containerWidth: 800
  }

  test('应该正确渲染矩阵容器', () => {
    render(<FeatureB1_1 {...defaultProps} />)
    
    // 检查矩阵文本是否存在
    expect(screen.getByText('矩阵')).toBeInTheDocument()
  })

  test('应该渲染初始化按键', () => {
    render(<FeatureB1_1 {...defaultProps} />)
    
    // 检查初始化按键是否存在
    expect(screen.getByText('初始化')).toBeInTheDocument()
  })

  test('应该渲染坐标按键', () => {
    render(<FeatureB1_1 {...defaultProps} />)
    
    // 检查坐标按键是否存在
    expect(screen.getByText('坐标')).toBeInTheDocument()
  })

  test('应该正确计算容器尺寸', () => {
    const { container } = render(<FeatureB1_1 {...defaultProps} />)
    
    const featureContainer = container.firstChild as HTMLElement
    
    // 检查容器高度是否为父容器的15%
    expect(featureContainer.style.height).toBe('90px') // 600 * 0.15
    
    // 检查容器宽度是否为父容器的94%
    expect(featureContainer.style.width).toBe('752px') // 800 * 0.94
  })

  test('应该有正确的背景颜色', () => {
    const { container } = render(<FeatureB1_1 {...defaultProps} />)
    
    const featureContainer = container.firstChild as HTMLElement
    expect(featureContainer.style.backgroundColor).toBe('rgb(190, 190, 190)') // #bebebe
  })

  test('按键应该可以点击', () => {
    render(<FeatureB1_1 {...defaultProps} />)

    const initButton = screen.getByText('初始化')
    const coordButton = screen.getByText('坐标')

    // 测试按键是否可以点击（不会抛出错误）
    fireEvent.click(initButton)
    fireEvent.click(coordButton)
  })

  test('坐标按键应该能够正确切换状态', () => {
    render(<FeatureB1_1 {...defaultProps} />)

    const coordButton = screen.getByText('坐标')

    // 第一次点击坐标按键
    fireEvent.click(coordButton)
    expect(mockSetCoordinateButton).toHaveBeenCalledWith(true)

    // 清除之前的调用记录
    mockSetCoordinateButton.mockClear()

    // 第二次点击坐标按键应该切换状态
    fireEvent.click(coordButton)
    expect(mockSetCoordinateButton).toHaveBeenCalledWith(false)
  })

  test('坐标按键应该响应外部状态变化', () => {
    // 重新mock store以返回激活状态
    jest.doMock('../../../frontend/features/logic/logicB1_1', () => ({
      useMatrixStore: () => ({
        coordinateButtonActive: true,
        setInitializeButton: mockSetInitializeButton,
        setCoordinateButton: mockSetCoordinateButton,
      })
    }))

    const { rerender } = render(<FeatureB1_1 {...defaultProps} />)

    // 验证按键以激活状态渲染
    const coordButton = screen.getByText('坐标')
    expect(coordButton).toBeInTheDocument()

    // 重新渲染以测试状态同步
    rerender(<FeatureB1_1 {...defaultProps} />)
    expect(coordButton).toBeInTheDocument()
  })
})
