/** @type {import('next').NextConfig} */
const nextConfig = {
  // 设置源代码目录为 frontend
  experimental: {
    // appDir 在 Next.js 13.4+ 中已经默认启用，不需要显式设置
  },
  // 自定义源目录
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
  // 设置应用目录
  distDir: '.next',
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: false,
  },
}

module.exports = nextConfig
