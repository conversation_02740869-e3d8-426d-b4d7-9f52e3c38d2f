/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/index.ts":
/*!***************************************!*\
  !*** ./frontend/ButtonCodes/index.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUTTON_TEXT: () => (/* reexport safe */ _secondary_style_style_secondary__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_BUTTON_TEXT),\n/* harmony export */   SecondaryButton: () => (/* reexport safe */ _secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_0__.SecondaryButton),\n/* harmony export */   SecondaryButtonEventHandler: () => (/* reexport safe */ _secondary_event_event_secondary__WEBPACK_IMPORTED_MODULE_2__.SecondaryButtonEventHandler),\n/* harmony export */   createSecondaryButtonEventHandler: () => (/* reexport safe */ _secondary_event_event_secondary__WEBPACK_IMPORTED_MODULE_2__.createSecondaryButtonEventHandler),\n/* harmony export */   secondaryButtonStyles: () => (/* reexport safe */ _secondary_style_style_secondary__WEBPACK_IMPORTED_MODULE_1__.secondaryButtonStyles)\n/* harmony export */ });\n/* harmony import */ var _secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secondary/SecondaryButton/SecondaryButton */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _secondary_style_style_secondary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./secondary/style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* harmony import */ var _secondary_event_event_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./secondary/event/event_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\");\n// 按键组件统一导出入口\n// 导出普通按键组件\n\n// 导出普通按键样式\n\n// 导出普通按键事件\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL0J1dHRvbkNvZGVzL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUEsYUFBYTtBQUViLFdBQVc7QUFDbUU7QUFHOUUsV0FBVztBQUlnQztBQUczQyxXQUFXO0FBSWdDIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvQnV0dG9uQ29kZXMvaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5oyJ6ZSu57uE5Lu257uf5LiA5a+85Ye65YWl5Y+jXG5cbi8vIOWvvOWHuuaZrumAmuaMiemUrue7hOS7tlxuZXhwb3J0IHsgU2Vjb25kYXJ5QnV0dG9uIH0gZnJvbSAnLi9zZWNvbmRhcnkvU2Vjb25kYXJ5QnV0dG9uL1NlY29uZGFyeUJ1dHRvbic7XG5leHBvcnQgdHlwZSB7IFNlY29uZGFyeUJ1dHRvblByb3BzIH0gZnJvbSAnLi9zZWNvbmRhcnkvU2Vjb25kYXJ5QnV0dG9uL1NlY29uZGFyeUJ1dHRvbic7XG5cbi8vIOWvvOWHuuaZrumAmuaMiemUruagt+W8j1xuZXhwb3J0IHsgXG4gIHNlY29uZGFyeUJ1dHRvblN0eWxlcywgXG4gIERFRkFVTFRfQlVUVE9OX1RFWFQgXG59IGZyb20gJy4vc2Vjb25kYXJ5L3N0eWxlL3N0eWxlX3NlY29uZGFyeSc7XG5leHBvcnQgdHlwZSB7IFNlY29uZGFyeUJ1dHRvblN0eWxlcyB9IGZyb20gJy4vc2Vjb25kYXJ5L3N0eWxlL3N0eWxlX3NlY29uZGFyeSc7XG5cbi8vIOWvvOWHuuaZrumAmuaMiemUruS6i+S7tlxuZXhwb3J0IHsgXG4gIFNlY29uZGFyeUJ1dHRvbkV2ZW50SGFuZGxlcixcbiAgY3JlYXRlU2Vjb25kYXJ5QnV0dG9uRXZlbnRIYW5kbGVyIFxufSBmcm9tICcuL3NlY29uZGFyeS9ldmVudC9ldmVudF9zZWNvbmRhcnknO1xuZXhwb3J0IHR5cGUgeyBcbiAgQnV0dG9uTW9kZSwgXG4gIFNlY29uZGFyeUJ1dHRvblN0YXRlLFxuICBTZWNvbmRhcnlCdXR0b25FdmVudHMgXG59IGZyb20gJy4vc2Vjb25kYXJ5L2V2ZW50L2V2ZW50X3NlY29uZGFyeSc7XG4iXSwibmFtZXMiOlsiU2Vjb25kYXJ5QnV0dG9uIiwic2Vjb25kYXJ5QnV0dG9uU3R5bGVzIiwiREVGQVVMVF9CVVRUT05fVEVYVCIsIlNlY29uZGFyeUJ1dHRvbkV2ZW50SGFuZGxlciIsImNyZWF0ZVNlY29uZGFyeUJ1dHRvbkV2ZW50SGFuZGxlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* harmony import */ var _event_event_secondary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../event/event_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false } = param;\n    _s();\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 创建事件处理器\n    const eventHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[eventHandler]\": ()=>{\n            return (0,_event_event_secondary__WEBPACK_IMPORTED_MODULE_3__.createSecondaryButtonEventHandler)(mode, {\n                onClick: {\n                    \"SecondaryButton.useMemo[eventHandler]\": (active)=>{\n                        setIsActive(active);\n                        onClick === null || onClick === void 0 ? void 0 : onClick(active);\n                    }\n                }[\"SecondaryButton.useMemo[eventHandler]\"],\n                onMouseEnter,\n                onMouseLeave,\n                onMouseDown,\n                onMouseUp\n            });\n        }\n    }[\"SecondaryButton.useMemo[eventHandler]\"], [\n        mode,\n        onClick,\n        onMouseEnter,\n        onMouseLeave,\n        onMouseDown,\n        onMouseUp\n    ]);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            if (disabled) {\n                return {\n                    ...baseStyles,\n                    ...customStyles,\n                    opacity: 0.6,\n                    cursor: 'not-allowed'\n                };\n            }\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            return {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            const newActiveState = eventHandler.handleClick();\n            setIsActive(newActiveState);\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            eventHandler.handleMouseEnter();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            eventHandler.handleMouseLeave();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            eventHandler.handleMouseDown();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            eventHandler.handleMouseUp();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        eventHandler\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"Hu7mUcVmi51WF3SCpAOMp6qdv/I=\");\n_c = SecondaryButton;\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/event/event_secondary.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButtonEventHandler: () => (/* binding */ SecondaryButtonEventHandler),\n/* harmony export */   createSecondaryButtonEventHandler: () => (/* binding */ createSecondaryButtonEventHandler)\n/* harmony export */ });\n// 普通按键事件类型定义\n// 普通按键事件处理类\nclass SecondaryButtonEventHandler {\n    // 获取当前状态\n    getState() {\n        return {\n            ...this.state\n        };\n    }\n    // 设置模式\n    setMode(mode) {\n        this.state.mode = mode;\n    }\n    // 设置激活状态\n    setActive(isActive) {\n        this.state.isActive = isActive;\n    }\n    // 处理点击事件\n    handleClick() {\n        if (this.state.mode === 'toggle') {\n            // 持续状态模式：切换并保持状态\n            this.state.isActive = !this.state.isActive;\n        } else if (this.state.mode === 'instant') {\n            // 瞬时状态模式：瞬间激活\n            this.state.isActive = true;\n        }\n        // 调用外部点击事件\n        if (this.events.onClick) {\n            this.events.onClick(this.state.isActive);\n        }\n        return this.state.isActive;\n    }\n    // 处理鼠标进入事件\n    handleMouseEnter() {\n        if (this.events.onMouseEnter) {\n            this.events.onMouseEnter();\n        }\n    }\n    // 处理鼠标离开事件\n    handleMouseLeave() {\n        if (this.events.onMouseLeave) {\n            this.events.onMouseLeave();\n        }\n    }\n    // 处理鼠标按下事件\n    handleMouseDown() {\n        if (this.state.mode === 'instant') {\n            this.state.isActive = true;\n        }\n        if (this.events.onMouseDown) {\n            this.events.onMouseDown();\n        }\n    }\n    // 处理鼠标松开事件\n    handleMouseUp() {\n        if (this.state.mode === 'instant') {\n            this.state.isActive = false;\n        }\n        if (this.events.onMouseUp) {\n            this.events.onMouseUp();\n        }\n    }\n    // 更新事件处理器\n    updateEvents(events) {\n        this.events = {\n            ...this.events,\n            ...events\n        };\n    }\n    constructor(initialState = {\n        isActive: false,\n        mode: 'toggle'\n    }, events = {}){\n        this.state = initialState;\n        this.events = events;\n    }\n}\n// 默认事件处理器工厂函数\nconst createSecondaryButtonEventHandler = function() {\n    let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'toggle', events = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    return new SecondaryButtonEventHandler({\n        isActive: false,\n        mode\n    }, events);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/style/style_secondary.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUTTON_TEXT: () => (/* binding */ DEFAULT_BUTTON_TEXT),\n/* harmony export */   secondaryButtonStyleConfig: () => (/* binding */ secondaryButtonStyleConfig),\n/* harmony export */   secondaryButtonStyles: () => (/* binding */ secondaryButtonStyles)\n/* harmony export */ });\n// 普通按键样式配置\nconst secondaryButtonStyles = {\n    // 默认样式\n    default: {\n        width: '200px',\n        height: '50px',\n        borderRadius: '0px',\n        backgroundColor: '#f1f1f1',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        cursor: 'pointer',\n        fontSize: '20px',\n        color: '#242424',\n        textAlign: 'center',\n        lineHeight: 1.5,\n        border: 'none',\n        outline: 'none',\n        transition: 'background-color 0.2s ease'\n    },\n    // 持续状态模式样式\n    toggle: {\n        active: {\n            backgroundColor: '#929292',\n            hover: '#858585'\n        },\n        inactive: {\n            backgroundColor: '#f1f1f1',\n            hover: '#e4e4e4'\n        }\n    },\n    // 瞬时状态模式样式\n    instant: {\n        hover: '#e4e4e4',\n        active: '#858585',\n        default: '#f1f1f1'\n    }\n};\n// 默认文本\nconst DEFAULT_BUTTON_TEXT = '按键';\n// 普通按键样式配置\nconst secondaryButtonStyleConfig = {\n    styles: secondaryButtonStyles,\n    defaultText: DEFAULT_BUTTON_TEXT\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL0J1dHRvbkNvZGVzL3NlY29uZGFyeS9zdHlsZS9zdHlsZV9zZWNvbmRhcnkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBNkNBLFdBQVc7QUFDSixNQUFNQSx3QkFBK0M7SUFDMUQsT0FBTztJQUNQQyxTQUFTO1FBQ1BDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsU0FBUztRQUNUQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxZQUFZO0lBQ2Q7SUFDQSxXQUFXO0lBQ1hDLFFBQVE7UUFDTkMsUUFBUTtZQUNOYixpQkFBaUI7WUFDakJjLE9BQU87UUFDVDtRQUNBQyxVQUFVO1lBQ1JmLGlCQUFpQjtZQUNqQmMsT0FBTztRQUNUO0lBQ0Y7SUFDQSxXQUFXO0lBQ1hFLFNBQVM7UUFDUEYsT0FBTztRQUNQRCxRQUFRO1FBQ1JqQixTQUFTO0lBQ1g7QUFDRixFQUFFO0FBRUYsT0FBTztBQUNBLE1BQU1xQixzQkFBc0IsS0FBSztBQUV4QyxXQUFXO0FBQ0osTUFBTUMsNkJBQXlEO0lBQ3BFQyxRQUFReEI7SUFDUnlCLGFBQWFIO0FBQ2YsRUFBRSIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL0J1dHRvbkNvZGVzL3NlY29uZGFyeS9zdHlsZS9zdHlsZV9zZWNvbmRhcnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuLy8g5pmu6YCa5oyJ6ZSu5bGe5oCn5o6l5Y+jXG5leHBvcnQgaW50ZXJmYWNlIFNlY29uZGFyeUJ1dHRvblByb3BzIHtcbiAgdGV4dD86IHN0cmluZztcbiAgbW9kZT86ICd0b2dnbGUnIHwgJ2luc3RhbnQnO1xuICBkaXNhYmxlZD86IGJvb2xlYW47XG4gIGluaXRpYWxBY3RpdmU/OiBib29sZWFuO1xuICBjdXN0b21TdHlsZXM/OiBSZWFjdC5DU1NQcm9wZXJ0aWVzO1xuICBvbkNsaWNrPzogKGlzQWN0aXZlOiBib29sZWFuKSA9PiB2b2lkO1xuICBvbk1vdXNlRG93bj86ICgpID0+IHZvaWQ7XG4gIG9uTW91c2VVcD86ICgpID0+IHZvaWQ7XG4gIG9uTW91c2VFbnRlcj86ICgpID0+IHZvaWQ7XG4gIG9uTW91c2VMZWF2ZT86ICgpID0+IHZvaWQ7XG59XG5cbi8vIOaZrumAmuaMiemUruagt+W8j+mFjee9ruaOpeWPo1xuZXhwb3J0IGludGVyZmFjZSBTZWNvbmRhcnlCdXR0b25TdHlsZUNvbmZpZyB7XG4gIHN0eWxlczogU2Vjb25kYXJ5QnV0dG9uU3R5bGVzO1xuICBkZWZhdWx0VGV4dDogc3RyaW5nO1xufVxuXG4vLyDmma7pgJrmjInplK7moLflvI/lrprkuYlcbmV4cG9ydCBpbnRlcmZhY2UgU2Vjb25kYXJ5QnV0dG9uU3R5bGVzIHtcbiAgLy8g6buY6K6k5qC35byPXG4gIGRlZmF1bHQ6IFJlYWN0LkNTU1Byb3BlcnRpZXM7XG4gIC8vIOaMgee7reeKtuaAgeaooeW8j+agt+W8j1xuICB0b2dnbGU6IHtcbiAgICBhY3RpdmU6IHtcbiAgICAgIGJhY2tncm91bmRDb2xvcjogc3RyaW5nO1xuICAgICAgaG92ZXI6IHN0cmluZztcbiAgICB9O1xuICAgIGluYWN0aXZlOiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IHN0cmluZztcbiAgICAgIGhvdmVyOiBzdHJpbmc7XG4gICAgfTtcbiAgfTtcbiAgLy8g556s5pe254q25oCB5qih5byP5qC35byPXG4gIGluc3RhbnQ6IHtcbiAgICBob3Zlcjogc3RyaW5nO1xuICAgIGFjdGl2ZTogc3RyaW5nO1xuICAgIGRlZmF1bHQ6IHN0cmluZztcbiAgfTtcbn1cblxuLy8g5pmu6YCa5oyJ6ZSu5qC35byP6YWN572uXG5leHBvcnQgY29uc3Qgc2Vjb25kYXJ5QnV0dG9uU3R5bGVzOiBTZWNvbmRhcnlCdXR0b25TdHlsZXMgPSB7XG4gIC8vIOm7mOiupOagt+W8j1xuICBkZWZhdWx0OiB7XG4gICAgd2lkdGg6ICcyMDBweCcsIC8vIOiuvue9ruWuveW6puS4uueItuWuueWZqOeahCA1MCVcbiAgICBoZWlnaHQ6ICc1MHB4JyxcbiAgICBib3JkZXJSYWRpdXM6ICcwcHgnLFxuICAgIGJhY2tncm91bmRDb2xvcjogJyNmMWYxZjEnLCAvLyDpu5jorqTog4zmma/oibJcbiAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgIGZvbnRTaXplOiAnMjBweCcsXG4gICAgY29sb3I6ICcjMjQyNDI0JyxcbiAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgIGxpbmVIZWlnaHQ6IDEuNSxcbiAgICBib3JkZXI6ICdub25lJyxcbiAgICBvdXRsaW5lOiAnbm9uZScsXG4gICAgdHJhbnNpdGlvbjogJ2JhY2tncm91bmQtY29sb3IgMC4ycyBlYXNlJyxcbiAgfSxcbiAgLy8g5oyB57ut54q25oCB5qih5byP5qC35byPXG4gIHRvZ2dsZTogeyAvLyDmv4DmtLvnirbmgIFcbiAgICBhY3RpdmU6IHtcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJyM5MjkyOTInLCAvLyDmjInplK7mv4DmtLvpopzoibJcbiAgICAgIGhvdmVyOiAnIzg1ODU4NScsIC8vIOaMiemUruaCrOWBnOminOiJslxuICAgIH0sXG4gICAgaW5hY3RpdmU6IHsgLy8g5pyq5r+A5rS754q25oCBXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjFmMWYxJywgLy8g5oyJ6ZSu5pyq5r+A5rS76aKc6ImyXG4gICAgICBob3ZlcjogJyNlNGU0ZTQnLCAvLyDmjInplK7mgqzlgZzpopzoibJcbiAgICB9LFxuICB9LFxuICAvLyDnnqzml7bnirbmgIHmqKHlvI/moLflvI9cbiAgaW5zdGFudDoge1xuICAgIGhvdmVyOiAnI2U0ZTRlNCcsIC8vIOaMiemUruaCrOWBnOminOiJslxuICAgIGFjdGl2ZTogJyM4NTg1ODUnLCAvLyDmjInplK7mjInkuIvpopzoibJcbiAgICBkZWZhdWx0OiAnI2YxZjFmMScsIC8vIOm7mOiupOiDjOaZr+iJslxuICB9LFxufTtcblxuLy8g6buY6K6k5paH5pysXG5leHBvcnQgY29uc3QgREVGQVVMVF9CVVRUT05fVEVYVCA9ICfmjInplK4nO1xuXG4vLyDmma7pgJrmjInplK7moLflvI/phY3nva5cbmV4cG9ydCBjb25zdCBzZWNvbmRhcnlCdXR0b25TdHlsZUNvbmZpZzogU2Vjb25kYXJ5QnV0dG9uU3R5bGVDb25maWcgPSB7XG4gIHN0eWxlczogc2Vjb25kYXJ5QnV0dG9uU3R5bGVzLFxuICBkZWZhdWx0VGV4dDogREVGQVVMVF9CVVRUT05fVEVYVCxcbn07XG4iXSwibmFtZXMiOlsic2Vjb25kYXJ5QnV0dG9uU3R5bGVzIiwiZGVmYXVsdCIsIndpZHRoIiwiaGVpZ2h0IiwiYm9yZGVyUmFkaXVzIiwiYmFja2dyb3VuZENvbG9yIiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImN1cnNvciIsImZvbnRTaXplIiwiY29sb3IiLCJ0ZXh0QWxpZ24iLCJsaW5lSGVpZ2h0IiwiYm9yZGVyIiwib3V0bGluZSIsInRyYW5zaXRpb24iLCJ0b2dnbGUiLCJhY3RpdmUiLCJob3ZlciIsImluYWN0aXZlIiwiaW5zdGFudCIsIkRFRkFVTFRfQlVUVE9OX1RFWFQiLCJzZWNvbmRhcnlCdXR0b25TdHlsZUNvbmZpZyIsInN0eWxlcyIsImRlZmF1bHRUZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/Store/store.ts":
/*!*********************************!*\
  !*** ./frontend/Store/store.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContainerStore: () => (/* binding */ useContainerStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n\nconst useContainerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        activeContainer: 'mode',\n        setActiveContainer: (container)=>set({\n                activeContainer: container\n            })\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL1N0b3JlL3N0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDO0FBT3pCLE1BQU1DLG9CQUFvQkQsK0NBQU1BLENBQWlCLENBQUNFLE1BQVM7UUFDaEVDLGlCQUFpQjtRQUNqQkMsb0JBQW9CLENBQUNDLFlBQWNILElBQUk7Z0JBQUVDLGlCQUFpQkU7WUFBVTtJQUN0RSxJQUFHIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvU3RvcmUvc3RvcmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCdcblxuaW50ZXJmYWNlIENvbnRhaW5lclN0YXRlIHtcbiAgYWN0aXZlQ29udGFpbmVyOiAnbW9kZScgfCAnYnVzaW5lc3MnXG4gIHNldEFjdGl2ZUNvbnRhaW5lcjogKGNvbnRhaW5lcjogJ21vZGUnIHwgJ2J1c2luZXNzJykgPT4gdm9pZFxufVxuXG5leHBvcnQgY29uc3QgdXNlQ29udGFpbmVyU3RvcmUgPSBjcmVhdGU8Q29udGFpbmVyU3RhdGU+KChzZXQpID0+ICh7XG4gIGFjdGl2ZUNvbnRhaW5lcjogJ21vZGUnLCAvLyDpu5jorqTmmL7npLrmqKHlvI/lrrnlmahcbiAgc2V0QWN0aXZlQ29udGFpbmVyOiAoY29udGFpbmVyKSA9PiBzZXQoeyBhY3RpdmVDb250YWluZXI6IGNvbnRhaW5lciB9KVxufSkpXG4iXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlQ29udGFpbmVyU3RvcmUiLCJzZXQiLCJhY3RpdmVDb250YWluZXIiLCJzZXRBY3RpdmVDb250YWluZXIiLCJjb250YWluZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/Store/store.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetA1.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetA1.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ComponetA1 = ()=>{\n    const containerStyle = {\n        height: '95vh',\n        width: '95vh',\n        backgroundColor: '#6d6d6d',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetA1.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponetA1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetA1);\nvar _c;\n$RefreshReg$(_c, \"ComponetA1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9jb21wb25ldEExLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUV5QjtBQUV6QixNQUFNQyxhQUF1QjtJQUMzQixNQUFNQyxpQkFBc0M7UUFDMUNDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakJDLFNBQVM7UUFDVEMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU9WOzs7Ozs7QUFJaEI7S0FqQk1EO0FBbUJOLGlFQUFlQSxVQUFVQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L2NvbXBvbmV0QTEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmNvbnN0IENvbXBvbmV0QTE6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBjb250YWluZXJTdHlsZTogUmVhY3QuQ1NTUHJvcGVydGllcyA9IHtcbiAgICBoZWlnaHQ6ICc5NXZoJyxcbiAgICB3aWR0aDogJzk1dmgnLFxuICAgIGJhY2tncm91bmRDb2xvcjogJyM2ZDZkNmQnLFxuICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e2NvbnRhaW5lclN0eWxlfT5cbiAgICAgIHsvKiDkuIDnuqflrrnlmajlhoXlrrnljLrln58gKi99XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgQ29tcG9uZXRBMVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29tcG9uZXRBMSIsImNvbnRhaW5lclN0eWxlIiwiaGVpZ2h0Iiwid2lkdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm92ZXJmbG93IiwiZGl2Iiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetA1.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetB1.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetB1.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_feature_featureB1_1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/feature/featureB1_1 */ \"(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst ComponetB1 = (param)=>{\n    let { isVisible } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [containerDimensions, setContainerDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // 监听容器尺寸变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ComponetB1.useEffect\": ()=>{\n            const updateDimensions = {\n                \"ComponetB1.useEffect.updateDimensions\": ()=>{\n                    if (containerRef.current) {\n                        const { offsetWidth, offsetHeight } = containerRef.current;\n                        setContainerDimensions({\n                            width: offsetWidth,\n                            height: offsetHeight\n                        });\n                    }\n                }\n            }[\"ComponetB1.useEffect.updateDimensions\"];\n            updateDimensions();\n            window.addEventListener('resize', updateDimensions);\n            return ({\n                \"ComponetB1.useEffect\": ()=>window.removeEventListener('resize', updateDimensions)\n            })[\"ComponetB1.useEffect\"];\n        }\n    }[\"ComponetB1.useEffect\"], [\n        isVisible\n    ]);\n    const containerStyle = {\n        height: '100%',\n        width: '100%',\n        backgroundColor: '#6d6d6d',\n        display: isVisible ? 'flex' : 'none',\n        flexDirection: 'column',\n        justifyContent: 'flex-start',\n        alignItems: 'center',\n        overflow: 'hidden',\n        position: 'relative'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        style: containerStyle,\n        children: isVisible && containerDimensions.width > 0 && containerDimensions.height > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_feature_featureB1_1__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            containerHeight: containerDimensions.height,\n            containerWidth: containerDimensions.width\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB1.tsx\",\n            lineNumber: 45,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB1.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponetB1, \"+Fd9xc5CKAi0bp0GTJIuq6ZzKOI=\");\n_c = ComponetB1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetB1);\nvar _c;\n$RefreshReg$(_c, \"ComponetB1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetB1.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetB2.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetB2.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ComponetB2 = (param)=>{\n    let { isVisible } = param;\n    const containerStyle = {\n        height: '100%',\n        width: '100%',\n        backgroundColor: '#6d6d6d',\n        display: isVisible ? 'flex' : 'none',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB2.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponetB2;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetB2);\nvar _c;\n$RefreshReg$(_c, \"ComponetB2\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9jb21wb25ldEIyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUV5QjtBQU16QixNQUFNQyxhQUF3QztRQUFDLEVBQUVDLFNBQVMsRUFBRTtJQUMxRCxNQUFNQyxpQkFBc0M7UUFDMUNDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakJDLFNBQVNMLFlBQVksU0FBUztRQUM5Qk0sZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU9WOzs7Ozs7QUFJaEI7S0FqQk1GO0FBbUJOLGlFQUFlQSxVQUFVQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L2NvbXBvbmV0QjIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBDb21wb25ldEIyUHJvcHMge1xuICBpc1Zpc2libGU6IGJvb2xlYW5cbn1cblxuY29uc3QgQ29tcG9uZXRCMjogUmVhY3QuRkM8Q29tcG9uZXRCMlByb3BzPiA9ICh7IGlzVmlzaWJsZSB9KSA9PiB7XG4gIGNvbnN0IGNvbnRhaW5lclN0eWxlOiBSZWFjdC5DU1NQcm9wZXJ0aWVzID0ge1xuICAgIGhlaWdodDogJzEwMCUnLFxuICAgIHdpZHRoOiAnMTAwJScsXG4gICAgYmFja2dyb3VuZENvbG9yOiAnIzZkNmQ2ZCcsXG4gICAgZGlzcGxheTogaXNWaXNpYmxlID8gJ2ZsZXgnIDogJ25vbmUnLFxuICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBvdmVyZmxvdzogJ2hpZGRlbidcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17Y29udGFpbmVyU3R5bGV9PlxuICAgICAgey8qIOS4muWKoeWuueWZqOWGheWuueWMuuWfnyAqL31cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBDb21wb25ldEIyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb21wb25ldEIyIiwiaXNWaXNpYmxlIiwiY29udGFpbmVyU3R5bGUiLCJoZWlnaHQiLCJ3aWR0aCIsImJhY2tncm91bmRDb2xvciIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwib3ZlcmZsb3ciLCJkaXYiLCJzdHlsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetB2.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/componetButton.tsx":
/*!********************************************************!*\
  !*** ./frontend/componets/componet/componetButton.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ButtonCodes */ \"(app-pages-browser)/./frontend/ButtonCodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ComponetButton = (param)=>{\n    let { onModeClick, onBusinessClick, isModeActive, isBusinessActive } = param;\n    const containerStyle = {\n        height: '3vh',\n        width: '20vw',\n        backgroundColor: 'transparent',\n        display: 'flex',\n        flexDirection: 'row',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden',\n        position: 'absolute',\n        top: '0',\n        left: '0'\n    };\n    const buttonStyle = {\n        height: '100%',\n        width: '50%'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                mode: \"toggle\",\n                onClick: onModeClick,\n                disabled: isModeActive,\n                customStyles: buttonStyle,\n                text: \"模式\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                mode: \"toggle\",\n                onClick: onBusinessClick,\n                disabled: isBusinessActive,\n                customStyles: buttonStyle,\n                text: \"业务\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ComponetButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetButton);\nvar _c;\n$RefreshReg$(_c, \"ComponetButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/componetButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/componet/main_container.tsx":
/*!********************************************************!*\
  !*** ./frontend/componets/componet/main_container.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _componetA1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./componetA1 */ \"(app-pages-browser)/./frontend/componets/componet/componetA1.tsx\");\n/* harmony import */ var _interaction_componet_interactionB__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interaction/componet_interactionB */ \"(app-pages-browser)/./frontend/componets/interaction/componet_interactionB.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst MainContainer = ()=>{\n    const containerStyle = {\n        height: '100vh',\n        width: '100vw',\n        backgroundColor: '#242424',\n        display: 'flex',\n        flexDirection: 'row',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componetA1__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_interaction_componet_interactionB__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MainContainer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainContainer);\nvar _c;\n$RefreshReg$(_c, \"MainContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9tYWluX2NvbnRhaW5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV5QjtBQUNZO0FBQ2tDO0FBRXZFLE1BQU1HLGdCQUEwQjtJQUM5QixNQUFNQyxpQkFBc0M7UUFDMUNDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakJDLFNBQVM7UUFDVEMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU9WOzswQkFDViw4REFBQ0gsbURBQVVBOzs7OzswQkFDWCw4REFBQ0MsMEVBQW9CQTs7Ozs7Ozs7Ozs7QUFHM0I7S0FsQk1DO0FBb0JOLGlFQUFlQSxhQUFhQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L21haW5fY29udGFpbmVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IENvbXBvbmV0QTEgZnJvbSAnLi9jb21wb25ldEExJ1xuaW1wb3J0IENvbXBvbmV0SW50ZXJhY3Rpb25CIGZyb20gJy4uL2ludGVyYWN0aW9uL2NvbXBvbmV0X2ludGVyYWN0aW9uQidcblxuY29uc3QgTWFpbkNvbnRhaW5lcjogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRhaW5lclN0eWxlOiBSZWFjdC5DU1NQcm9wZXJ0aWVzID0ge1xuICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICB3aWR0aDogJzEwMHZ3JyxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMjQyNDI0JyxcbiAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXtjb250YWluZXJTdHlsZX0+XG4gICAgICA8Q29tcG9uZXRBMSAvPlxuICAgICAgPENvbXBvbmV0SW50ZXJhY3Rpb25CIC8+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgTWFpbkNvbnRhaW5lclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29tcG9uZXRBMSIsIkNvbXBvbmV0SW50ZXJhY3Rpb25CIiwiTWFpbkNvbnRhaW5lciIsImNvbnRhaW5lclN0eWxlIiwiaGVpZ2h0Iiwid2lkdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm92ZXJmbG93IiwiZGl2Iiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/componet/main_container.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/componets/interaction/componet_interactionB.tsx":
/*!******************************************************************!*\
  !*** ./frontend/componets/interaction/componet_interactionB.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Store_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Store/store */ \"(app-pages-browser)/./frontend/Store/store.ts\");\n/* harmony import */ var _componet_componetB1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../componet/componetB1 */ \"(app-pages-browser)/./frontend/componets/componet/componetB1.tsx\");\n/* harmony import */ var _componet_componetB2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../componet/componetB2 */ \"(app-pages-browser)/./frontend/componets/componet/componetB2.tsx\");\n/* harmony import */ var _componet_componetButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../componet/componetButton */ \"(app-pages-browser)/./frontend/componets/componet/componetButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ComponetInteractionB = ()=>{\n    _s();\n    const { activeContainer, setActiveContainer } = (0,_Store_store__WEBPACK_IMPORTED_MODULE_2__.useContainerStore)();\n    const handleModeClick = ()=>{\n        if (activeContainer !== 'mode') {\n            setActiveContainer('mode');\n        }\n    };\n    const handleBusinessClick = ()=>{\n        if (activeContainer !== 'business') {\n            setActiveContainer('business');\n        }\n    };\n    const containerStyle = {\n        position: 'relative',\n        height: '95vh',\n        width: '20vw',\n        marginLeft: '1vw'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetB1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: activeContainer === 'mode'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetB2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: activeContainer === 'business'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onModeClick: handleModeClick,\n                onBusinessClick: handleBusinessClick,\n                isModeActive: activeContainer === 'mode',\n                isBusinessActive: activeContainer === 'business'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ComponetInteractionB, \"LCJEIS0QlTe7jl9Tgc1mJy1Yiik=\", false, function() {\n    return [\n        _Store_store__WEBPACK_IMPORTED_MODULE_2__.useContainerStore\n    ];\n});\n_c = ComponetInteractionB;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetInteractionB);\nvar _c;\n$RefreshReg$(_c, \"ComponetInteractionB\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/componets/interaction/componet_interactionB.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx":
/*!***************************************************!*\
  !*** ./frontend/features/feature/featureB1_1.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/ButtonCodes/secondary/SecondaryButton/SecondaryButton */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/logic/logicB1_1 */ \"(app-pages-browser)/./frontend/features/logic/logicB1_1.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FeatureB1_1 = (param)=>{\n    let { containerHeight, containerWidth } = param;\n    _s();\n    const { setInitializeButton, setCoordinateButton } = (0,_features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore)();\n    // 容器样式\n    const containerStyle = {\n        position: 'relative',\n        height: \"\".concat(containerHeight * 0.15, \"px\"),\n        width: \"\".concat(containerWidth * 0.94, \"px\"),\n        backgroundColor: '#bebebe',\n        overflow: 'hidden',\n        top: \"\".concat(containerHeight * 0.06, \"px\") // 顶部对齐：占componetB1容器高的6%\n    };\n    // 文本样式\n    const textStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.2, \"px\"),\n        left: \"\".concat(containerWidth * 0.94 * 0.01, \"px\"),\n        fontSize: '30px',\n        color: '#242424'\n    };\n    // 初始化按键样式\n    const initButtonStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.55, \"px\"),\n        left: \"\".concat(containerWidth * 0.94 * 0.04, \"px\"),\n        height: \"\".concat(containerHeight * 0.15 * 0.35, \"px\"),\n        width: \"\".concat(containerWidth * 0.94 * 0.44, \"px\") // 键宽：占featureB1_1容器宽的44%\n    };\n    // 坐标按键样式\n    const coordButtonStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.55, \"px\"),\n        right: \"\".concat(containerWidth * 0.94 * 0.04, \"px\"),\n        height: \"\".concat(containerHeight * 0.15 * 0.35, \"px\"),\n        width: \"\".concat(containerWidth * 0.94 * 0.44, \"px\") // 键宽：占featureB1_1容器宽的44%\n    };\n    // 初始化按键点击处理\n    const handleInitializeClick = (isActive)=>{\n        setInitializeButton(isActive);\n        if (isActive) {\n            setCoordinateButton(false); // 重置坐标按键状态为false\n        }\n    };\n    // 坐标按键点击处理\n    const handleCoordinateClick = (isActive)=>{\n        setCoordinateButton(isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: textStyle,\n                children: \"矩阵\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: initButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"instant\",\n                    text: \"初始化\",\n                    onClick: handleInitializeClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: coordButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    text: \"坐标\",\n                    onClick: handleCoordinateClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FeatureB1_1, \"wfO//lNk4OqSiVetaNDF/VhuCnk=\", false, function() {\n    return [\n        _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore\n    ];\n});\n_c = FeatureB1_1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeatureB1_1);\nvar _c;\n$RefreshReg$(_c, \"FeatureB1_1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./frontend/features/logic/logicB1_1.ts":
/*!**********************************************!*\
  !*** ./frontend/features/logic/logicB1_1.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMatrixStore: () => (/* binding */ useMatrixStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n\n// 创建矩阵功能状态管理store\nconst useMatrixStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        // 初始状态\n        initializeButtonActive: false,\n        coordinateButtonActive: false,\n        // 设置初始化按键状态\n        setInitializeButton: (active)=>set((state)=>({\n                    initializeButtonActive: active,\n                    // 当初始化按键激活时，重置坐标按键状态\n                    coordinateButtonActive: active ? false : state.coordinateButtonActive\n                })),\n        // 设置坐标按键状态\n        setCoordinateButton: (active)=>set({\n                coordinateButtonActive: active\n            }),\n        // 重置所有按键状态\n        resetAllButtons: ()=>set({\n                initializeButtonActive: false,\n                coordinateButtonActive: false\n            })\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/features/logic/logicB1_1.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./frontend/componets/componet/main_container.tsx */ \"(app-pages-browser)/./frontend/componets/componet/main_container.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRmZyb250ZW5kJTJGY29tcG9uZXRzJTJGY29tcG9uZXQlMkZtYWluX2NvbnRhaW5lci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsME5BQXFLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9tYWluX2NvbnRhaW5lci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zustand/esm/react.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/react.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/vanilla */ \"(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS9yZWFjdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwQjtBQUNvQjs7QUFFOUM7QUFDQTtBQUNBLGdCQUFnQix1REFBMEI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLGdEQUFtQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxjQUFjLDREQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3Qvbm9kZV9tb2R1bGVzL3p1c3RhbmQvZXNtL3JlYWN0Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tICd6dXN0YW5kL3ZhbmlsbGEnO1xuXG5jb25zdCBpZGVudGl0eSA9IChhcmcpID0+IGFyZztcbmZ1bmN0aW9uIHVzZVN0b3JlKGFwaSwgc2VsZWN0b3IgPSBpZGVudGl0eSkge1xuICBjb25zdCBzbGljZSA9IFJlYWN0LnVzZVN5bmNFeHRlcm5hbFN0b3JlKFxuICAgIGFwaS5zdWJzY3JpYmUsXG4gICAgKCkgPT4gc2VsZWN0b3IoYXBpLmdldFN0YXRlKCkpLFxuICAgICgpID0+IHNlbGVjdG9yKGFwaS5nZXRJbml0aWFsU3RhdGUoKSlcbiAgKTtcbiAgUmVhY3QudXNlRGVidWdWYWx1ZShzbGljZSk7XG4gIHJldHVybiBzbGljZTtcbn1cbmNvbnN0IGNyZWF0ZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgY29uc3QgYXBpID0gY3JlYXRlU3RvcmUoY3JlYXRlU3RhdGUpO1xuICBjb25zdCB1c2VCb3VuZFN0b3JlID0gKHNlbGVjdG9yKSA9PiB1c2VTdG9yZShhcGksIHNlbGVjdG9yKTtcbiAgT2JqZWN0LmFzc2lnbih1c2VCb3VuZFN0b3JlLCBhcGkpO1xuICByZXR1cm4gdXNlQm91bmRTdG9yZTtcbn07XG5jb25zdCBjcmVhdGUgPSAoY3JlYXRlU3RhdGUpID0+IGNyZWF0ZVN0YXRlID8gY3JlYXRlSW1wbChjcmVhdGVTdGF0ZSkgOiBjcmVhdGVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGUsIHVzZVN0b3JlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zustand/esm/react.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS92YW5pbGxhLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4SEFBOEg7QUFDOUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS92YW5pbGxhLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVTdG9yZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgbGV0IHN0YXRlO1xuICBjb25zdCBsaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICBjb25zdCBzZXRTdGF0ZSA9IChwYXJ0aWFsLCByZXBsYWNlKSA9PiB7XG4gICAgY29uc3QgbmV4dFN0YXRlID0gdHlwZW9mIHBhcnRpYWwgPT09IFwiZnVuY3Rpb25cIiA/IHBhcnRpYWwoc3RhdGUpIDogcGFydGlhbDtcbiAgICBpZiAoIU9iamVjdC5pcyhuZXh0U3RhdGUsIHN0YXRlKSkge1xuICAgICAgY29uc3QgcHJldmlvdXNTdGF0ZSA9IHN0YXRlO1xuICAgICAgc3RhdGUgPSAocmVwbGFjZSAhPSBudWxsID8gcmVwbGFjZSA6IHR5cGVvZiBuZXh0U3RhdGUgIT09IFwib2JqZWN0XCIgfHwgbmV4dFN0YXRlID09PSBudWxsKSA/IG5leHRTdGF0ZSA6IE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCBuZXh0U3RhdGUpO1xuICAgICAgbGlzdGVuZXJzLmZvckVhY2goKGxpc3RlbmVyKSA9PiBsaXN0ZW5lcihzdGF0ZSwgcHJldmlvdXNTdGF0ZSkpO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgZ2V0U3RhdGUgPSAoKSA9PiBzdGF0ZTtcbiAgY29uc3QgZ2V0SW5pdGlhbFN0YXRlID0gKCkgPT4gaW5pdGlhbFN0YXRlO1xuICBjb25zdCBzdWJzY3JpYmUgPSAobGlzdGVuZXIpID0+IHtcbiAgICBsaXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICByZXR1cm4gKCkgPT4gbGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gIH07XG4gIGNvbnN0IGFwaSA9IHsgc2V0U3RhdGUsIGdldFN0YXRlLCBnZXRJbml0aWFsU3RhdGUsIHN1YnNjcmliZSB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBzdGF0ZSA9IGNyZWF0ZVN0YXRlKHNldFN0YXRlLCBnZXRTdGF0ZSwgYXBpKTtcbiAgcmV0dXJuIGFwaTtcbn07XG5jb25zdCBjcmVhdGVTdG9yZSA9IChjcmVhdGVTdGF0ZSkgPT4gY3JlYXRlU3RhdGUgPyBjcmVhdGVTdG9yZUltcGwoY3JlYXRlU3RhdGUpIDogY3JlYXRlU3RvcmVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGVTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);