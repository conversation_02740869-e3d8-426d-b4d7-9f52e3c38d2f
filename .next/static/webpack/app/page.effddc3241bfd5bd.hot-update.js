"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx":
/*!***************************************************!*\
  !*** ./frontend/features/feature/featureB1_1.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/ButtonCodes/secondary/SecondaryButton/SecondaryButton */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/logic/logicB1_1 */ \"(app-pages-browser)/./frontend/features/logic/logicB1_1.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FeatureB1_1 = (param)=>{\n    let { containerHeight, containerWidth } = param;\n    _s();\n    const { coordinateButtonActive, setInitializeButton, setCoordinateButton } = (0,_features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore)();\n    // 容器样式\n    const containerStyle = {\n        position: 'relative',\n        height: \"\".concat(containerHeight * 0.15, \"px\"),\n        width: \"\".concat(containerWidth * 0.94, \"px\"),\n        backgroundColor: '#bebebe',\n        overflow: 'hidden',\n        top: \"\".concat(containerHeight * 0.06, \"px\") // 顶部对齐：占componetB1容器高的6%\n    };\n    // 文本样式\n    const textStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.2, \"px\"),\n        left: \"\".concat(containerWidth * 0.94 * 0.01, \"px\"),\n        fontSize: '30px',\n        color: '#242424'\n    };\n    // 初始化按键样式\n    const initButtonStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.55, \"px\"),\n        left: \"\".concat(containerWidth * 0.94 * 0.04, \"px\"),\n        height: \"\".concat(containerHeight * 0.15 * 0.35, \"px\"),\n        width: \"\".concat(containerWidth * 0.94 * 0.44, \"px\") // 键宽：占featureB1_1容器宽的44%\n    };\n    // 坐标按键样式\n    const coordButtonStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.55, \"px\"),\n        right: \"\".concat(containerWidth * 0.94 * 0.04, \"px\"),\n        height: \"\".concat(containerHeight * 0.15 * 0.35, \"px\"),\n        width: \"\".concat(containerWidth * 0.94 * 0.44, \"px\") // 键宽：占featureB1_1容器宽的44%\n    };\n    // 初始化按键点击处理\n    const handleInitializeClick = (isActive)=>{\n        setInitializeButton(isActive);\n        if (isActive) {\n            setCoordinateButton(false); // 重置坐标按键状态为false\n        }\n    };\n    // 坐标按键点击处理\n    const handleCoordinateClick = (isActive)=>{\n        setCoordinateButton(isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: textStyle,\n                children: \"矩阵\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: initButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"instant\",\n                    text: \"初始化\",\n                    onClick: handleInitializeClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: coordButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"toggle\",\n                    text: \"坐标\",\n                    initialActive: coordinateButtonActive,\n                    onClick: handleCoordinateClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, \"coordinate-\".concat(coordinateButtonActive), false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FeatureB1_1, \"jf7GlwlvnBV7wvuTJMJDYUHJ2tM=\", false, function() {\n    return [\n        _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore\n    ];\n});\n_c = FeatureB1_1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeatureB1_1);\nvar _c;\n$RefreshReg$(_c, \"FeatureB1_1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx\n"));

/***/ })

});