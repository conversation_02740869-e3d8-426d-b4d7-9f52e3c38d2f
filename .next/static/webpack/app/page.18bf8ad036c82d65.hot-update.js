"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/style/style_secondary.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUTTON_TEXT: () => (/* binding */ DEFAULT_BUTTON_TEXT),\n/* harmony export */   secondaryButtonStyleConfig: () => (/* binding */ secondaryButtonStyleConfig),\n/* harmony export */   secondaryButtonStyles: () => (/* binding */ secondaryButtonStyles)\n/* harmony export */ });\n// 普通按键样式配置\nconst secondaryButtonStyles = {\n    // 默认样式\n    default: {\n        width: '200px',\n        height: '50px',\n        borderRadius: '0px',\n        backgroundColor: '#f1f1f1',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        cursor: 'pointer',\n        fontSize: '20px',\n        color: '#242424',\n        textAlign: 'center',\n        lineHeight: 1.5,\n        border: 'none',\n        outline: 'none',\n        transition: 'background-color 0.2s ease'\n    },\n    // 持续状态模式样式\n    toggle: {\n        active: {\n            backgroundColor: '#929292',\n            hover: '#858585'\n        },\n        inactive: {\n            backgroundColor: '#f1f1f1',\n            hover: '#e4e4e4'\n        }\n    },\n    // 瞬时状态模式样式\n    instant: {\n        hover: '#e4e4e4',\n        active: '#858585',\n        default: '#f1f1f1'\n    }\n};\n// 默认文本\nconst DEFAULT_BUTTON_TEXT = '按键';\n// 普通按键样式配置\nconst secondaryButtonStyleConfig = {\n    styles: secondaryButtonStyles,\n    defaultText: DEFAULT_BUTTON_TEXT\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2Zyb250ZW5kL0J1dHRvbkNvZGVzL3NlY29uZGFyeS9zdHlsZS9zdHlsZV9zZWNvbmRhcnkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBNkNBLFdBQVc7QUFDSixNQUFNQSx3QkFBK0M7SUFDMUQsT0FBTztJQUNQQyxTQUFTO1FBQ1BDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsU0FBUztRQUNUQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxZQUFZO0lBQ2Q7SUFDQSxXQUFXO0lBQ1hDLFFBQVE7UUFDTkMsUUFBUTtZQUNOYixpQkFBaUI7WUFDakJjLE9BQU87UUFDVDtRQUNBQyxVQUFVO1lBQ1JmLGlCQUFpQjtZQUNqQmMsT0FBTztRQUNUO0lBQ0Y7SUFDQSxXQUFXO0lBQ1hFLFNBQVM7UUFDUEYsT0FBTztRQUNQRCxRQUFRO1FBQ1JqQixTQUFTO0lBQ1g7QUFDRixFQUFFO0FBRUYsT0FBTztBQUNBLE1BQU1xQixzQkFBc0IsS0FBSztBQUV4QyxXQUFXO0FBQ0osTUFBTUMsNkJBQXlEO0lBQ3BFQyxRQUFReEI7SUFDUnlCLGFBQWFIO0FBQ2YsRUFBRSIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL0J1dHRvbkNvZGVzL3NlY29uZGFyeS9zdHlsZS9zdHlsZV9zZWNvbmRhcnkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuLy8g5pmu6YCa5oyJ6ZSu5bGe5oCn5o6l5Y+jXG5leHBvcnQgaW50ZXJmYWNlIFNlY29uZGFyeUJ1dHRvblByb3BzIHtcbiAgdGV4dD86IHN0cmluZztcbiAgbW9kZT86ICd0b2dnbGUnIHwgJ2luc3RhbnQnO1xuICBkaXNhYmxlZD86IGJvb2xlYW47XG4gIGluaXRpYWxBY3RpdmU/OiBib29sZWFuO1xuICBjdXN0b21TdHlsZXM/OiBSZWFjdC5DU1NQcm9wZXJ0aWVzO1xuICBvbkNsaWNrPzogKGlzQWN0aXZlOiBib29sZWFuKSA9PiB2b2lkO1xuICBvbk1vdXNlRG93bj86ICgpID0+IHZvaWQ7XG4gIG9uTW91c2VVcD86ICgpID0+IHZvaWQ7XG4gIG9uTW91c2VFbnRlcj86ICgpID0+IHZvaWQ7XG4gIG9uTW91c2VMZWF2ZT86ICgpID0+IHZvaWQ7XG59XG5cbi8vIOaZrumAmuaMiemUruagt+W8j+mFjee9ruaOpeWPo1xuZXhwb3J0IGludGVyZmFjZSBTZWNvbmRhcnlCdXR0b25TdHlsZUNvbmZpZyB7XG4gIHN0eWxlczogU2Vjb25kYXJ5QnV0dG9uU3R5bGVzO1xuICBkZWZhdWx0VGV4dDogc3RyaW5nO1xufVxuXG4vLyDmma7pgJrmjInplK7moLflvI/lrprkuYlcbmV4cG9ydCBpbnRlcmZhY2UgU2Vjb25kYXJ5QnV0dG9uU3R5bGVzIHtcbiAgLy8g6buY6K6k5qC35byPXG4gIGRlZmF1bHQ6IFJlYWN0LkNTU1Byb3BlcnRpZXM7XG4gIC8vIOaMgee7reeKtuaAgeaooeW8j+agt+W8j1xuICB0b2dnbGU6IHtcbiAgICBhY3RpdmU6IHtcbiAgICAgIGJhY2tncm91bmRDb2xvcjogc3RyaW5nO1xuICAgICAgaG92ZXI6IHN0cmluZztcbiAgICB9O1xuICAgIGluYWN0aXZlOiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IHN0cmluZztcbiAgICAgIGhvdmVyOiBzdHJpbmc7XG4gICAgfTtcbiAgfTtcbiAgLy8g556s5pe254q25oCB5qih5byP5qC35byPXG4gIGluc3RhbnQ6IHtcbiAgICBob3Zlcjogc3RyaW5nO1xuICAgIGFjdGl2ZTogc3RyaW5nO1xuICAgIGRlZmF1bHQ6IHN0cmluZztcbiAgfTtcbn1cblxuLy8g5pmu6YCa5oyJ6ZSu5qC35byP6YWN572uXG5leHBvcnQgY29uc3Qgc2Vjb25kYXJ5QnV0dG9uU3R5bGVzOiBTZWNvbmRhcnlCdXR0b25TdHlsZXMgPSB7XG4gIC8vIOm7mOiupOagt+W8j1xuICBkZWZhdWx0OiB7XG4gICAgd2lkdGg6ICcyMDBweCcsIC8vIOiuvue9ruWuveW6puS4uueItuWuueWZqOeahCA1MCVcbiAgICBoZWlnaHQ6ICc1MHB4JyxcbiAgICBib3JkZXJSYWRpdXM6ICcwcHgnLFxuICAgIGJhY2tncm91bmRDb2xvcjogJyNmMWYxZjEnLFxuICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgZm9udFNpemU6ICcyMHB4JyxcbiAgICBjb2xvcjogJyMyNDI0MjQnLFxuICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgbGluZUhlaWdodDogMS41LFxuICAgIGJvcmRlcjogJ25vbmUnLFxuICAgIG91dGxpbmU6ICdub25lJyxcbiAgICB0cmFuc2l0aW9uOiAnYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2UnLFxuICB9LFxuICAvLyDmjIHnu63nirbmgIHmqKHlvI/moLflvI9cbiAgdG9nZ2xlOiB7IC8vIOa/gOa0u+eKtuaAgVxuICAgIGFjdGl2ZToge1xuICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzkyOTI5MicsXG4gICAgICBob3ZlcjogJyM4NTg1ODUnLFxuICAgIH0sXG4gICAgaW5hY3RpdmU6IHsgLy8g5pyq5r+A5rS754q25oCBXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjFmMWYxJyxcbiAgICAgIGhvdmVyOiAnI2U0ZTRlNCcsXG4gICAgfSxcbiAgfSxcbiAgLy8g556s5pe254q25oCB5qih5byP5qC35byPXG4gIGluc3RhbnQ6IHtcbiAgICBob3ZlcjogJyNlNGU0ZTQnLFxuICAgIGFjdGl2ZTogJyM4NTg1ODUnLFxuICAgIGRlZmF1bHQ6ICcjZjFmMWYxJyxcbiAgfSxcbn07XG5cbi8vIOm7mOiupOaWh+acrFxuZXhwb3J0IGNvbnN0IERFRkFVTFRfQlVUVE9OX1RFWFQgPSAn5oyJ6ZSuJztcblxuLy8g5pmu6YCa5oyJ6ZSu5qC35byP6YWN572uXG5leHBvcnQgY29uc3Qgc2Vjb25kYXJ5QnV0dG9uU3R5bGVDb25maWc6IFNlY29uZGFyeUJ1dHRvblN0eWxlQ29uZmlnID0ge1xuICBzdHlsZXM6IHNlY29uZGFyeUJ1dHRvblN0eWxlcyxcbiAgZGVmYXVsdFRleHQ6IERFRkFVTFRfQlVUVE9OX1RFWFQsXG59O1xuIl0sIm5hbWVzIjpbInNlY29uZGFyeUJ1dHRvblN0eWxlcyIsImRlZmF1bHQiLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsImJhY2tncm91bmRDb2xvciIsImRpc3BsYXkiLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJjdXJzb3IiLCJmb250U2l6ZSIsImNvbG9yIiwidGV4dEFsaWduIiwibGluZUhlaWdodCIsImJvcmRlciIsIm91dGxpbmUiLCJ0cmFuc2l0aW9uIiwidG9nZ2xlIiwiYWN0aXZlIiwiaG92ZXIiLCJpbmFjdGl2ZSIsImluc3RhbnQiLCJERUZBVUxUX0JVVFRPTl9URVhUIiwic2Vjb25kYXJ5QnV0dG9uU3R5bGVDb25maWciLCJzdHlsZXMiLCJkZWZhdWx0VGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\n"));

/***/ })

});