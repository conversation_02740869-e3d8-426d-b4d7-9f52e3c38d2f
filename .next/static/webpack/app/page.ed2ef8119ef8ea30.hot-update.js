"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx":
/*!***************************************************!*\
  !*** ./frontend/features/feature/featureB1_1.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/ButtonCodes/secondary/SecondaryButton/SecondaryButton */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/logic/logicB1_1 */ \"(app-pages-browser)/./frontend/features/logic/logicB1_1.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FeatureB1_1 = (param)=>{\n    let { containerHeight, containerWidth } = param;\n    _s();\n    const { coordinateButtonActive, setInitializeButton, setCoordinateButton } = (0,_features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore)();\n    // 容器样式\n    const containerStyle = {\n        position: 'relative',\n        height: \"\".concat(containerHeight * 0.15, \"px\"),\n        width: \"\".concat(containerWidth * 0.94, \"px\"),\n        backgroundColor: '#bebebe',\n        overflow: 'hidden',\n        top: \"\".concat(containerHeight * 0.06, \"px\") // 顶部对齐：占componetB1容器高的6%\n    };\n    // 文本样式\n    const textStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.2, \"px\"),\n        left: \"\".concat(containerWidth * 0.94 * 0.01, \"px\"),\n        fontSize: '30px',\n        color: '#242424'\n    };\n    // 初始化按键样式\n    const initButtonStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.55, \"px\"),\n        left: \"\".concat(containerWidth * 0.94 * 0.04, \"px\"),\n        height: \"\".concat(containerHeight * 0.15 * 0.35, \"px\"),\n        width: \"\".concat(containerWidth * 0.94 * 0.44, \"px\") // 键宽：占featureB1_1容器宽的44%\n    };\n    // 坐标按键样式\n    const coordButtonStyle = {\n        position: 'absolute',\n        top: \"\".concat(containerHeight * 0.15 * 0.55, \"px\"),\n        right: \"\".concat(containerWidth * 0.94 * 0.04, \"px\"),\n        height: \"\".concat(containerHeight * 0.15 * 0.35, \"px\"),\n        width: \"\".concat(containerWidth * 0.94 * 0.44, \"px\") // 键宽：占featureB1_1容器宽的44%\n    };\n    // 初始化按键点击处理\n    const handleInitializeClick = (isActive)=>{\n        setInitializeButton(isActive);\n        if (isActive) {\n            setCoordinateButton(false); // 重置坐标按键状态为false\n        }\n    };\n    // 坐标按键点击处理\n    const handleCoordinateClick = (isActive)=>{\n        setCoordinateButton(isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: textStyle,\n                children: \"矩阵\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: initButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"instant\",\n                    text: \"初始化\",\n                    onClick: handleInitializeClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: coordButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"toggle\",\n                    text: \"坐标\",\n                    initialActive: coordinateButtonActive,\n                    onClick: handleCoordinateClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, \"coordinate-\".concat(coordinateButtonActive), false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FeatureB1_1, \"jf7GlwlvnBV7wvuTJMJDYUHJ2tM=\", false, function() {\n    return [\n        _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore\n    ];\n});\n_c = FeatureB1_1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeatureB1_1);\nvar _c;\n$RefreshReg$(_c, \"FeatureB1_1\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/features/feature/featureB1_1.tsx\n"));

/***/ })

});