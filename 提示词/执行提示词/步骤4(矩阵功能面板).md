# 矩阵功能面板

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.阅读‘三级容器(矩阵).md’并用于创建文件
- 3.严格执行‘三级容器(矩阵).md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测代码能否正常运行
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行矩阵功能面板的开发任务：

1. **技术栈准备**：首先阅读项目根目录下的'前端技术栈.md'文档，了解项目使用的前端技术栈和开发规范，并严格按照文档中的技术选型和代码规范来构建代码
2. **需求分析**：阅读'三级容器(矩阵).md'文档，理解矩阵功能面板的具体需求、组件结构和实现要求，并基于该文档的指示创建相应的文件和目录结构
3. **严格执行规范**：完全按照'三级容器(矩阵).md'文档中的详细指示进行开发，包括但不限于：
   - 组件命名规范
   - 文件目录结构
   - 功能实现逻辑
   - 样式和交互要求
4. **文档阅读限制**：在完成当前任务之前，严禁阅读除上述两个指定文档以外的任何其他项目文档，避免信息干扰和需求混淆
5. **任务范围控制**：仅实现当前指令中明确要求的功能逻辑，不得自行添加额外功能或进行功能扩展，确保开发范围的精确控制
6. **代码质量验证**：完成代码编写后，进行以下检测：
   - 语法错误检查
   - 功能逻辑验证
   - 组件渲染测试
   - 确保代码能够正常编译和运行
7. **项目配置更新**：根据新增的文件和依赖，相应更新项目根目录下的.gitignore文件，确保不必要的文件不被版本控制追踪

注意：请严格按照1-7的顺序执行，每完成一步后再进行下一步，确保开发流程的规范性和可控性。
