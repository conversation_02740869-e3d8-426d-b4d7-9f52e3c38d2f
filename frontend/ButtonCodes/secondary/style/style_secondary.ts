import React from 'react';

// 普通按键属性接口
export interface SecondaryButtonProps {
  text?: string;
  mode?: 'toggle' | 'instant';
  disabled?: boolean;
  initialActive?: boolean;
  customStyles?: React.CSSProperties;
  onClick?: (isActive: boolean) => void;
  onMouseDown?: () => void;
  onMouseUp?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

// 普通按键样式配置接口
export interface SecondaryButtonStyleConfig {
  styles: SecondaryButtonStyles;
  defaultText: string;
}

// 普通按键样式定义
export interface SecondaryButtonStyles {
  // 默认样式
  default: React.CSSProperties;
  // 持续状态模式样式
  toggle: {
    active: {
      backgroundColor: string;
      hover: string;
    };
    inactive: {
      backgroundColor: string;
      hover: string;
    };
  };
  // 瞬时状态模式样式
  instant: {
    hover: string;
    active: string;
    default: string;
  };
}

// 普通按键样式配置
export const secondaryButtonStyles: SecondaryButtonStyles = {
  // 默认样式
  default: {
    width: '200px', // 设置宽度为父容器的 50%
    height: '50px',
    borderRadius: '0px',
    backgroundColor: '#f1f1f1', // 默认背景色
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    fontSize: '20px',
    color: '#242424',
    textAlign: 'center',
    lineHeight: 1.5,
    border: 'none',
    outline: 'none',
    transition: 'background-color 0.2s ease',
  },
  // 持续状态模式样式
  toggle: { // 激活状态
    active: {
      backgroundColor: '#929292', // 按键激活颜色
      hover: '#858585', // 按键悬停颜色
    },
    inactive: { // 未激活状态
      backgroundColor: '#f1f1f1', // 按键未激活颜色
      hover: '#e4e4e4', // 按键悬停颜色
    },
  },
  // 瞬时状态模式样式
  instant: {
    hover: '#e4e4e4', // 按键悬停颜色
    active: '#858585', // 按键按下颜色
    default: '#f1f1f1', // 默认背景色
  },
};

// 默认文本
export const DEFAULT_BUTTON_TEXT = '按键';

// 普通按键样式配置
export const secondaryButtonStyleConfig: SecondaryButtonStyleConfig = {
  styles: secondaryButtonStyles,
  defaultText: DEFAULT_BUTTON_TEXT,
};
