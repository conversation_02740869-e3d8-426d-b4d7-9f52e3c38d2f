'use client'

import React from 'react'

interface ComponetB1Props {
  isVisible: boolean
}

const ComponetB1: React.FC<ComponetB1Props> = ({ isVisible }) => {
  const containerStyle: React.CSSProperties = {
    height: '100%',
    width: '100%',
    backgroundColor: '#6d6d6d',
    display: isVisible ? 'flex' : 'none',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  }

  return (
    <div style={containerStyle}>
      {/* 模式容器内容区域 */}
    </div>
  )
}

export default ComponetB1
