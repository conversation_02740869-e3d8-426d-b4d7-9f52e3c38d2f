'use client'

import React, { useRef, useEffect, useState } from 'react'
import FeatureB1_1 from '@/features/feature/featureB1_1'

interface ComponetB1Props {
  isVisible: boolean
}

const ComponetB1: React.FC<ComponetB1Props> = ({ isVisible }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 })

  // 监听容器尺寸变化
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current
        setContainerDimensions({ width: offsetWidth, height: offsetHeight })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)

    return () => window.removeEventListener('resize', updateDimensions)
  }, [isVisible])

  const containerStyle: React.CSSProperties = {
    height: '100%',
    width: '100%',
    backgroundColor: '#6d6d6d',
    display: isVisible ? 'flex' : 'none',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    overflow: 'hidden',
    position: 'relative'
  }

  return (
    <div ref={containerRef} style={containerStyle}>
      {/* 矩阵功能容器 */}
      {isVisible && containerDimensions.width > 0 && containerDimensions.height > 0 && (
        <FeatureB1_1
          containerHeight={containerDimensions.height}
          containerWidth={containerDimensions.width}
        />
      )}
    </div>
  )
}

export default ComponetB1
