'use client'

import React from 'react'
import ComponetA1 from './componetA1'
import ComponetInteractionB from '../interaction/componet_interactionB'

const MainContainer: React.FC = () => {
  const containerStyle: React.CSSProperties = {
    height: '100vh',
    width: '100vw',
    backgroundColor: '#242424',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  }

  return (
    <div style={containerStyle}>
      <ComponetA1 />
      <ComponetInteractionB />
    </div>
  )
}

export default MainContainer
