'use client'

import React from 'react'
import { useContainerStore } from '../../Store/store'
import ComponetB1 from '../componet/componetB1'
import ComponetB2 from '../componet/componetB2'
import ComponetButton from '../componet/componetButton'

const ComponetInteractionB: React.FC = () => {
  const { activeContainer, setActiveContainer } = useContainerStore()

  const handleModeClick = () => {
    if (activeContainer !== 'mode') {
      setActiveContainer('mode')
    }
  }

  const handleBusinessClick = () => {
    if (activeContainer !== 'business') {
      setActiveContainer('business')
    }
  }

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    height: '95vh',
    width: '20vw',
    marginLeft: '1vw'
  }

  return (
    <div style={containerStyle}>
      <ComponetB1 isVisible={activeContainer === 'mode'} />
      <ComponetB2 isVisible={activeContainer === 'business'} />
      <ComponetButton
        onModeClick={handleModeClick}
        onBusinessClick={handleBusinessClick}
        isModeActive={activeContainer === 'mode'}
        isBusinessActive={activeContainer === 'business'}
      />
    </div>
  )
}

export default ComponetInteractionB
