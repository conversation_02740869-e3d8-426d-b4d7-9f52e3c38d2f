'use client'

import React from 'react'
import { SecondaryButton } from '@/ButtonCodes/secondary/SecondaryButton/SecondaryButton'
import { useMatrixStore } from '@/features/logic/logicB1_1'

interface FeatureB1_1Props {
  containerHeight: number
  containerWidth: number
}

const FeatureB1_1: React.FC<FeatureB1_1Props> = ({ containerHeight, containerWidth }) => {
  const { setInitializeButton, setCoordinateButton } = useMatrixStore()

  // 容器样式
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    height: `${containerHeight * 0.15}px`, // 继承父容器的15%高度
    width: `${containerWidth * 0.94}px`, // 继承父容器的94%宽度
    backgroundColor: '#bebebe',
    overflow: 'hidden',
    top: `${containerHeight * 0.06}px` // 顶部对齐：占componetB1容器高的6%
  }

  // 文本样式
  const textStyle: React.CSSProperties = {
    position: 'absolute',
    top: `${containerHeight * 0.15 * 0.2}px`, // top: featureB1_1高度的20%
    left: `${containerWidth * 0.94 * 0.01}px`, // left: featureB1_1宽度的1%
    fontSize: '30px',
    color: '#242424'
  }

  // 初始化按键样式
  const initButtonStyle: React.CSSProperties = {
    position: 'absolute',
    top: `${containerHeight * 0.15 * 0.55}px`, // top: featureB1_1高度的55%
    left: `${containerWidth * 0.94 * 0.04}px`, // left: featureB1_1宽度的4%
    height: `${containerHeight * 0.15 * 0.35}px`, // 键高：占featureB1_1容器高的35%
    width: `${containerWidth * 0.94 * 0.44}px` // 键宽：占featureB1_1容器宽的44%
  }

  // 坐标按键样式
  const coordButtonStyle: React.CSSProperties = {
    position: 'absolute',
    top: `${containerHeight * 0.15 * 0.55}px`, // top: featureB1_1高度的55%
    right: `${containerWidth * 0.94 * 0.04}px`, // right: featureB1_1宽度的4%
    height: `${containerHeight * 0.15 * 0.35}px`, // 键高：占featureB1_1容器高的35%
    width: `${containerWidth * 0.94 * 0.44}px` // 键宽：占featureB1_1容器宽的44%
  }

  // 初始化按键点击处理
  const handleInitializeClick = (isActive: boolean) => {
    setInitializeButton(isActive)
    if (isActive) {
      setCoordinateButton(false) // 重置坐标按键状态为false
    }
  }

  // 坐标按键点击处理
  const handleCoordinateClick = (isActive: boolean) => {
    setCoordinateButton(isActive)
  }

  return (
    <div style={containerStyle}>
      {/* 默认文本：矩阵 */}
      <div style={textStyle}>矩阵</div>
      
      {/* 初始化按键 */}
      <div style={initButtonStyle}>
        <SecondaryButton
          mode="instant"
          text="初始化"
          onClick={handleInitializeClick}
          customStyles={{
            width: '100%',
            height: '100%',
            fontSize: 'inherit' // 尺寸自适应
          }}
        />
      </div>
      
      {/* 坐标按键 */}
      <div style={coordButtonStyle}>
        <SecondaryButton
          text="坐标"
          onClick={handleCoordinateClick}
          customStyles={{
            width: '100%',
            height: '100%',
            fontSize: 'inherit' // 尺寸自适应
          }}
        />
      </div>
    </div>
  )
}

export default FeatureB1_1
