import { create } from 'zustand'

// 矩阵功能状态接口
interface MatrixState {
  // 按键状态
  initializeButtonActive: boolean
  coordinateButtonActive: boolean
  
  // 状态更新方法
  setInitializeButton: (active: boolean) => void
  setCoordinateButton: (active: boolean) => void
  
  // 重置所有状态
  resetAllButtons: () => void
}

// 创建矩阵功能状态管理store
export const useMatrixStore = create<MatrixState>((set) => ({
  // 初始状态
  initializeButtonActive: false,
  coordinateButtonActive: false,
  
  // 设置初始化按键状态
  setInitializeButton: (active: boolean) => 
    set((state) => ({ 
      initializeButtonActive: active,
      // 当初始化按键激活时，重置坐标按键状态
      coordinateButtonActive: active ? false : state.coordinateButtonActive
    })),
  
  // 设置坐标按键状态
  setCoordinateButton: (active: boolean) => 
    set({ coordinateButtonActive: active }),
  
  // 重置所有按键状态
  resetAllButtons: () => 
    set({ 
      initializeButtonActive: false, 
      coordinateButtonActive: false 
    })
}))
